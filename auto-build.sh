#!/usr/bin/env bash

# 艾尔母婴商户管理平台 - 自动打包构建并上传镜像脚本
# 支持测试环境(staging)和生产环境(production)

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "使用方法:"
    echo "  $0 [环境选项]"
    echo ""
    echo "环境选项:"
    echo "  staging, test, s, t    - 构建测试环境镜像"
    echo "  production, prod, p    - 构建生产环境镜像"
    echo "  both, all, a          - 构建两个环境的镜像"
    echo "  -h, --help            - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 staging            # 构建测试环境"
    echo "  $0 production         # 构建生产环境"
    echo "  $0 both              # 构建两个环境"
}

# 检查必要的工具
check_requirements() {
    log_info "检查必要的工具..."
    
    # 检查 nvm
    if ! command -v nvm &> /dev/null; then
        if [ ! -s "$HOME/.nvm/nvm.sh" ]; then
            log_error "nvm 未安装或未正确配置"
            log_info "请安装 nvm: https://github.com/nvm-sh/nvm"
            exit 1
        else
            # 加载 nvm
            source "$HOME/.nvm/nvm.sh"
        fi
    fi
    
    # 检查 docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    # 检查 docker 是否运行
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行"
        exit 1
    fi
    
    log_success "所有必要工具检查通过"
}

# 设置 Node.js 版本
setup_nodejs() {
    log_info "设置 Node.js 版本为 16..."
    
    # 加载 nvm
    if [ -s "$HOME/.nvm/nvm.sh" ]; then
        source "$HOME/.nvm/nvm.sh"
    fi
    
    # 使用 Node.js 16
    nvm use 16
    
    if [ $? -ne 0 ]; then
        log_warning "Node.js 16 未安装，正在安装..."
        nvm install 16
        nvm use 16
    fi
    
    # 验证版本
    local node_version=$(node --version)
    log_success "当前 Node.js 版本: $node_version"
    
    # 检查 npm
    local npm_version=$(npm --version)
    log_info "当前 npm 版本: $npm_version"
}

# 安装依赖
install_dependencies() {
    log_info "检查并安装项目依赖..."
    
    if [ ! -d "node_modules" ] || [ ! -f "package-lock.json" ]; then
        log_info "安装项目依赖..."
        npm install
    else
        log_info "依赖已存在，跳过安装"
    fi
    
    log_success "依赖检查完成"
}

# 构建项目
build_project() {
    local env=$1
    local build_command=""
    local image_tag=""
    
    case $env in
        "staging")
            build_command="build:stage"
            image_tag="test"
            ;;
        "production")
            build_command="build:prod"
            image_tag="prod"
            ;;
        *)
            log_error "未知的环境: $env"
            exit 1
            ;;
    esac
    
    log_info "开始构建 $env 环境..."
    
    # 清理之前的构建
    if [ -d "dist" ]; then
        log_info "清理之前的构建文件..."
        rm -rf dist
    fi
    
    # 执行构建
    log_info "执行构建命令: npm run $build_command"
    npm run $build_command
    
    if [ $? -ne 0 ]; then
        log_error "$env 环境构建失败"
        exit 1
    fi
    
    log_success "$env 环境构建完成"
    
    # 构建 Docker 镜像
    build_docker_image $image_tag
    
    # 推送镜像
    push_docker_image $image_tag
}

# 构建 Docker 镜像
build_docker_image() {
    local tag=$1
    local image_name="registry.cn-shenzhen.aliyuncs.com/lnktech/aier-ui:$tag"
    
    log_info "构建 Docker 镜像: $image_name"
    
    docker build -t $image_name .
    
    if [ $? -ne 0 ]; then
        log_error "Docker 镜像构建失败"
        exit 1
    fi
    
    log_success "Docker 镜像构建完成: $image_name"
}

# 推送 Docker 镜像
push_docker_image() {
    local tag=$1
    local image_name="registry.cn-shenzhen.aliyuncs.com/lnktech/aier-ui:$tag"
    
    log_info "推送 Docker 镜像: $image_name"
    
    docker push $image_name
    
    if [ $? -ne 0 ]; then
        log_error "Docker 镜像推送失败"
        exit 1
    fi
    
    log_success "Docker 镜像推送完成: $image_name"
}

# 主函数
main() {
    local environment=""
    
    # 解析命令行参数
    case "${1:-}" in
        "staging"|"test"|"s"|"t")
            environment="staging"
            ;;
        "production"|"prod"|"p")
            environment="production"
            ;;
        "both"|"all"|"a")
            environment="both"
            ;;
        "-h"|"--help"|"help")
            show_usage
            exit 0
            ;;
        "")
            log_warning "未指定环境，请选择要构建的环境:"
            echo "1) staging (测试环境)"
            echo "2) production (生产环境)"
            echo "3) both (两个环境)"
            read -p "请输入选择 (1/2/3): " choice
            case $choice in
                1) environment="staging" ;;
                2) environment="production" ;;
                3) environment="both" ;;
                *) log_error "无效选择"; exit 1 ;;
            esac
            ;;
        *)
            log_error "未知参数: $1"
            show_usage
            exit 1
            ;;
    esac
    
    log_info "开始自动构建流程..."
    log_info "目标环境: $environment"
    
    # 执行构建流程
    check_requirements
    setup_nodejs
    install_dependencies
    
    case $environment in
        "staging")
            build_project "staging"
            ;;
        "production")
            build_project "production"
            ;;
        "both")
            build_project "staging"
            echo ""
            build_project "production"
            ;;
    esac
    
    log_success "所有构建任务完成！"
}

# 脚本入口
main "$@"
