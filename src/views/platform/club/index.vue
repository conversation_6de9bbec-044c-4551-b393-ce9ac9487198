<template>
  <div class="app-container" style="background: #ffff">
    <el-form
      :model="form"
      label-width="200px"
      :rules="rules"
      ref="form"
      v-loading="loading"
    >
      <h2>基本信息</h2>
      <el-divider></el-divider>
      <el-form-item label="LOGO" prop="logo">
        <template>
          <img-upload
            v-model="form.logo"
            width="150px"
            height="150px"
            :fixed-number="[100, 100]"
            border
          >
            <template #imgUpload>
              <div
                style="width: 100%; height: 100%"
                class="flex flex-direction-column flex-content-center flex-align-center"
              >
                <em
                  class="el-icon-circle-plus"
                  style="font-size: 23px; color: #b8b8b8"
                ></em>
                <span style="font-size: 14px; font-weight: 400; color: #0f0f0f"
                  >上传logo</span
                >
              </div>
            </template>
          </img-upload>
        </template>
      </el-form-item>
      <el-form-item label="会所名称" prop="clubName">
        <el-input
          v-model="form.clubName"
          maxlength="15"
          minlength="3"
          placeholder="请输入会所名称字符长度：3~15"
          style="width: 60%"
        ></el-input>
      </el-form-item>
      <el-form-item label="字体颜色" prop="nameColor">
        <el-color-picker
          v-model="form.nameColor"
          show-alpha
          :predefine="predefineColors"
        >
        </el-color-picker>
      </el-form-item>

      <el-form-item label="会所标签" :rules="[{ required: true }]">
        <el-tag
          :key="tag"
          v-for="tag in dynamicTags"
          closable
          :disable-transitions="false"
          @close="handleClose(tag)"
        >
          {{ tag }}
        </el-tag>
        <el-input
          class="input-new-tag"
          v-if="inputVisible"
          v-model="inputValue"
          ref="saveTagInput"
          size="small"
          @keyup.enter.native="handleInputConfirm"
          @blur="handleInputConfirm"
        >
        </el-input>
        <el-button v-else class="button-new-tag" size="small" @click="showInput"
          >+ 添加标签</el-button
        >
      </el-form-item>

      <el-form-item label="背景照片" prop="clubBackgroundPhotos">
        <image-upload
          :limit="1"
          :isShowTip="false"
          v-model="form.clubBackgroundPhotos"
        />
      </el-form-item>

      <el-form-item label="地址" prop="locationAddress">
        <el-input
          v-model="form.locationAddress"
          placeholder="请输入详情地址,格式：XX省XX市XX区（县）XXXXXX"
          style="width: 60%"
        ></el-input>
      </el-form-item>
      <el-form-item label="字体颜色" prop="addressColor">
        <el-color-picker
          v-model="form.addressColor"
          show-alpha
          :predefine="predefineColors"
        >
        </el-color-picker>
      </el-form-item>
      <el-form-item label="客服电话" prop="servicePhone">
        <el-input
          v-model="form.servicePhone"
          placeholder="请输入客服电话"
          style="width: 60%"
        ></el-input>
      </el-form-item>

      <!-- <h2>品牌介绍</h2>
      <el-divider></el-divider>

      <el-form-item label="Banner" prop="clubFacilityPhotos">
        <image-upload
          :limit="1"
          v-model="form.clubFacilityPhotos"
          :is-show-tip="false"
        />
      </el-form-item>

      <el-form-item label="会所介绍" prop="clubDescription">
        <el-input
          type="textarea"
          v-model="form.clubDescription"
          style="width: 60%"
          placeholder="请输入会所介绍"
          :rows="4"
        ></el-input>
      </el-form-item>

      <el-form-item label="图册" prop="clubFacilitiesPhotos">
        <image-upload
          :limit="12"
          v-model="form.clubFacilitiesPhotos"
          :is-show-tip="false"
        />
      </el-form-item> -->
      <h2>海报信息</h2>
      <el-divider></el-divider>
      <el-form-item label="会所宣传语" prop="posterSlogan">
        <el-input
          type="text"
          v-model="form.posterSlogan"
          style="width: 60%"
          placeholder="请输入会所宣传语, 20个字数以内"
          maxlength="20"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="护理宣传语" prop="nursePosterSlogan">
        <el-input
          type="text"
          v-model="form.nursePosterSlogan"
          style="width: 60%"
          placeholder="海报两行展示文字，需要用逗号隔开，第一行和第二行字数15字以内"
          maxlength="32"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="产康宣传语" prop="postpartumPosterSlogan">
        <el-input
          type="text"
          v-model="form.postpartumPosterSlogan"
          style="width: 60%"
          placeholder="海报两行展示文字，需要用逗号隔开，第一行和第二行字数15字以内"
          maxlength="32"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="厨师宣传语" prop="chefPosterSlogan">
        <el-input
          type="text"
          v-model="form.chefPosterSlogan"
          style="width: 60%"
          placeholder="海报两行展示文字，需要用逗号隔开，第一行和第二行字数15字以内"
          maxlength="32"
          clearable
        ></el-input>
      </el-form-item>
    </el-form>

    <div style="margin: 20px auto; text-align: center">
      <el-button type="primary" @click="submitForm">确定</el-button>
      <el-button @click="cancel">取消</el-button>
    </div>
  </div>
</template>

<script>
import { save, info, update } from "@/api/platform/indexConfig";
import ImgCrop from "@/components/ImgCrop/index";
export default {
  name: "CLUB_INFO",
  components: {
    ImgCrop,
  },
  data() {
    return {
      predefineColors: [
        "#ff4500",
        "#ff8c00",
        "#ffd700",
        "#90ee90",
        "#00ced1",
        "#1e90ff",
        "#c71585",
        "rgba(255, 69, 0, 0.68)",
        "rgb(255, 120, 0)",
        "hsv(51, 100, 98)",
        "hsva(120, 40, 94, 0.5)",
        "hsl(181, 100%, 37%)",
        "hsla(209, 100%, 56%, 0.73)",
        "#c7158577",
      ],
      inputVisible: false,
      inputValue: "",
      form: {
        nameColor: "#ffffff", //名字颜色
        addressColor: "#ffffff", //地址颜色
        clubName: "", //会所名称
        clubTagNames: [], //会所标签
        clubBackgroundPhotos: [], //背景照片
        clubActivityAreaPhotos: [], //活动区照片
        locationAddress: "", //店铺地址
        // clubFacilityPhotos: [], //会所照片
        // clubDescription: "", //会所介绍
        // clubFacilitiesPhotos: [], //会所设施照片
        bossTel: "", //管理者电话
        dialogVisible: false,
        desc: "",
        videos: [],
        clubActivities: [],
        servicePhone: "",
        logo: "",
        chefPosterSlogan: "",
        postpartumPosterSlogan: "",
        nursePosterSlogan: "",
        posterSlogan: "",
      },
      dynamicTags: [],
      rules: {
        logo: [
          { required: true, message: "会所logo不能为空", trigger: "blur" },
        ],
        clubName: [
          { required: true, message: "会所名称不能为空", trigger: "blur" },
        ],
        clubBackgroundPhotos: [
          { required: true, message: "背景图片不能为空", trigger: "blur" },
        ],
        locationAddress: [
          { required: true, message: "会所地址不能为空", trigger: "blur" },
        ],
        servicePhone: [
          { required: true, message: "会所客服电话不能为空", trigger: "blur" },
        ],
        clubFacilityPhotos: [
          { required: true, message: "品牌Banner不能为空", trigger: "blur" },
        ],
        clubDescription: [
          { required: true, message: "品牌介绍不能为空", trigger: "blur" },
        ],
        clubFacilitiesPhotos: [
          { required: true, message: "会所图册不能空为空", trigger: "blur" },
        ],
      },
      loading: false,
    };
  },
  created() {
    this.info();
  },
  watch: {
    dynamicTags() {
      if (this.dynamicTags.length > 0) {
        this.form.clubTagNames = this.dynamicTags;
      }
    },
  },
  methods: {
    cancel() {
      //取消
      this.info();
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.dynamicTags.length <= 0) {
            this.$modal.msgError("请至少添加一个会所标签");
            return;
          }
          this.loading = true;
          if (this.form.clubId != null) {
            update(this.form)
              .then((res) => {
                this.info();
              })
              .finally(() => {
                this.loading = false;
              });
          } else {
            save(this.form)
              .then((res) => {
                this.info();
              })
              .finally(() => {
                this.loading = false;
              });
          }
        }
      });
    },
    info() {
      this.loading = true;
      info()
        .then((res) => {
          this.form = res.data;
          this.dynamicTags = res.data.clubTagNames ? res.data.clubTagNames : [];
        })
        .finally(() => {
          this.loading = false;
        });
    },
    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    handleClose(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
    },
    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        if (this.dynamicTags.length >= 2) {
          this.$modal.msgError("最多添加两个标签");
        } else {
          this.dynamicTags.push(inputValue);
        }
      }
      this.inputVisible = false;
      this.inputValue = "";
    },
  },
};
</script>

<style scoped lang="scss">
.el-tag + .el-tag {
  margin-left: 10px;
}

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}

h2 {
  margin: 0px 0;
}

.el-divider {
  margin: 10px 0;
}
</style>
