<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
      class="query-conditions"
    >
      <el-form-item label="活动名称" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="活动名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item class="operation-group">
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>

      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="list"
      @selection-change="handleSuitesSelectionChange"
    >
      <el-table-column label="序号" min-width="50" align="center">
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>

      <el-table-column label="活动标题" align="center" width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.title }}</span>
        </template>
      </el-table-column>
      <el-table-column label="活动类型" align="center" prop="type">
        <template slot-scope="scope">
          <span>{{ scope.row.type == 1 ? "图文" : "视频" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="活动主图" align="center" prop="type">
        <template slot-scope="scope">
          <image-preview
            v-if="scope.row.type == 1"
            :src="scope.row.mainImage"
            :width="240"
            :height="135"
          ></image-preview>
          <video-preview
            v-else
            :src="scope.row.video"
            :width="240"
            :height="135"
          ></video-preview>
        </template>
      </el-table-column>
      <el-table-column label="活动开始时间" align="center" prop="type">
        <template slot-scope="scope">
          <span>{{ scope.row.startDate }}</span>
        </template>
      </el-table-column>
      <el-table-column label="活动结束时间" align="center" prop="type">
        <template slot-scope="scope">
          <span>{{ scope.row.endDate }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="活动状态" align="center" prop="type">
        <template slot-scope="scope">
          <span v-if="scope.row.status">已开始</span>
          <span v-if="!scope.row.status">已结束</span>
        </template>
      </el-table-column> -->
      <el-table-column label="创建时间" align="center" prop="type">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="200"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
          <el-button size="mini" type="text" @click="clickOnline(scope.row)">
            <p v-if="scope.row.isDisabled">下架</p>
            <p v-if="!scope.row.isDisabled">上架</p>
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <div style="height: 600px; overflow: auto">
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="标题" prop="title">
            <el-input
              v-model="form.title"
              type="text"
              placeholder="请输入活动标题"
            ></el-input>
          </el-form-item>
          <el-form-item label="活动开始时间" prop="title">
            <el-date-picker
              v-model="form.startDate"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              placeholder="选择活动开始时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="活动结束时间" prop="title">
            <el-date-picker
              v-model="form.endDate"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              placeholder="选择活动结束时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-radio-group v-model="form.type" size="medium">
              <el-radio-button :label="1">图文</el-radio-button>
              <el-radio-button :label="2">视频</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="主图" prop="mainImage" v-if="form.type == 1">
            <!-- <image-upload :limit="1" :isShowTip="false" v-model="form.mainImage"></image-upload> -->

            <template>
              <img-upload
                v-model="form.mainImage"
                width="480px"
                height="270px"
                :fixed-number="[480, 270]"
                border
              >
                <template #imgUpload>
                  <div
                    style="width: 100%; height: 100%"
                    class="flex flex-direction-column flex-content-center flex-align-center"
                  >
                    <em
                      class="el-icon-circle-plus"
                      style="font-size: 23px; color: #b8b8b8"
                    ></em>
                    <span
                      style="font-size: 14px; font-weight: 400; color: #0f0f0f"
                      >设置活动主图</span
                    >
                  </div>
                </template>
              </img-upload>
            </template>
          </el-form-item>
          <el-form-item label="内容" prop="content" v-if="form.type == 1">
            <editor :minHeight="300" v-model="form.content"></editor>
          </el-form-item>
          <el-form-item label="视频" prop="video" v-if="form.type == 2">
            <!-- <file-upload :limit="1" v-model="form.video" :file-type="['mp4']" :file-size="5"></file-upload> -->
            <video-upload v-model="form.video" />
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input-number
              v-model="form.sort"
              :min="0"
              :precision="0"
              label="请输入排序值 活动将根据值的大小排序"
              style="width: 100%"
            >
            </el-input-number>
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitForm"
          v-loading.fullscreen.lock="fullscreenLoading"
          >确定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  save,
  page,
  detail,
  update,
  remove,
  isDisabled,
} from "@/api/platform/clubActivity";
import VideoUpload from "@/components/VideoUpload/index";
import ImgUpload from "@/components/ImgUpload/index";
import ImagePreview from "@/components/ImagePreview/index";
import VideoPreview from "@/components/VideoPreview/index";
export default {
  name: "EmployeePost",
  components: {
    ImgUpload,
    VideoUpload,
    ImagePreview,
    VideoPreview,
  },
  data() {
    return {
      value1: "",
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 选中数组
      ids: [],
      multiple: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      form: {
        sort: 0,
        mainImage: undefined,
        video: undefined,
        title: "",
        type: 1,
        content: "",
      },
      rules: {
        title: [
          { required: true, message: "活动名称不能为空", trigger: "blur" },
        ],
        type: [
          { required: true, message: "活动类型不能为空", trigger: "blur" },
        ],
        sort: [{ required: true, message: "排序不能为空", trigger: "blur" }],
        content: [
          { required: true, message: "图文内容不能为空", trigger: "blur" },
        ],
        video: [
          { required: true, message: "视频文件不能为空", trigger: "blur" },
        ],
        mainImage: [
          { required: true, message: "活动主图不能为空", trigger: "blur" },
        ],
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      fullscreenLoading: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then((response) => {
        this.list = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getDetail(id) {
      this.fullscreenLoading = true;
      detail(id)
        .then((res) => {
          this.form = res.data;
        })
        .finally(() => {
          this.fullscreenLoading = false;
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        sort: 0,
        mainImage: undefined,
        video: undefined,
        title: "",
        type: 1,
        content: "",
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleSuitesSelectionChange(selection) {
      this.ids = selection.map((item) => item.suiteId);
      this.multiple = !selection.length;
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增活动";
    },
    handleUpdate(row) {
      const id = row.activityId;
      this.reset();
      this.open = true;
      this.title = "修改活动";
      this.getDetail(id);
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.fullscreenLoading = true;
          const action = this.form.activityId ? update : save;
          action(this.form)
            .then((res) => {
              this.$modal.msgSuccess(
                this.form.activityId ? "修改成功" : "新增成功"
              );
              this.getList();
            })
            .finally(() => {
              this.open = false;
              this.fullscreenLoading = false;
            });
        }
      });
    },
    handleDelete(row) {
      this.fullscreenLoading = true;
      remove(row.activityId)
        .then((res) => {
          this.$modal.msgSuccess("删除成功");
        })
        .finally(() => {
          this.getList();
          this.fullscreenLoading = false;
        });
    },
    clickOnline(row) {
      console.log(row.activityId);
      //上架下架
      isDisabled(row.activityId).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.getList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
  },
};
</script>

<style scoped>
.query-conditions {
  /* 整体布局 */
  padding: 16px 20px;
  background: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);

  /* 表单项样式 */
  .el-form-item {
    margin-bottom: 0;
    margin-right: 16px;

    /* 标签文字 */
    .el-form-item__label {
      color: #606266;
      font-size: 14px;
      line-height: 32px;
    }

    /* 输入框 */
    .el-input {
      width: 220px;

      .el-input__inner {
        height: 32px;
        line-height: 32px;
      }
    }
  }

  /* 按钮组 */
  .operation-group {
    display: flex;
    align-items: center;

    .el-button {
      padding: 8px 15px;
      margin-left: 8px;

      &:first-child {
        margin-left: 0;
      }
    }
  }

  /* 响应式布局 */
  @media screen and (max-width: 1366px) {
    .el-form-item {
      margin-bottom: 8px;
    }
  }
}
</style>
