<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入小程序名称" />
      </el-form-item>
      <el-form-item label="配置类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择配置类型"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.sys_wechat_config_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:wechat:mini:program:add']"
          >新增</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="wechatConfigList"
      @cell-click="goclick"
    >
      <el-table-column label="名称" align="center" prop="name" />
      <el-table-column
        label="配置类型"
        align="center"
        prop="type"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sys_wechat_config_type"
            :value="scope.row.type"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="APPID"
        align="center"
        prop="appid"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="SECRET"
        align="center"
        prop="secret"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="TOKEN"
        align="center"
        prop="token"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column
        label="AESKEY"
        align="center"
        prop="aesKey"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column
        label="消息格式"
        align="center"
        prop="msgDataFormat"
      ></el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否使用" align="center" prop="issued">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.issued"
            :active-value="true"
            :inactive-value="false"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-s-tools"
            @click="toCommercialPage(scope.row)"
            v-hasPermi="['system:wechat:mini:program:commercial']"
            >配置模板</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-s-tools"
            @click="toMerchantsPage(scope.row)"
            v-hasPermi="['system:wechat:mini:program:merchants']"
            >配置商户</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:wechat:mini:program:update']"
            >修改</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入小程序名称" />
        </el-form-item>
        <el-form-item label="配置类型" prop="type">
          <el-select
            v-model="form.type"
            placeholder="请选择配置类型"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in dict.type.sys_wechat_config_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="APPID" prop="appid">
          <el-input v-model="form.appid" placeholder="请输入APPID" />
        </el-form-item>
        <el-form-item label="SECRET" prop="secret">
          <el-input v-model="form.secret" placeholder="请输入SECRET" />
        </el-form-item>
        <el-form-item label="消息格式" prop="msgDataFormat">
          <el-select
            v-model="form.msgDataFormat"
            placeholder="请选择消息格式"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in messageFormatOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getWechatConfigPage,
  updateWechatConfig,
  saveWechatConfig,
  getWechatConfig,
  changeWechatConfigStatus,
} from "@/api/system/wechat";
import { queryOptions } from "@/api/system/tenant";

export default {
  name: "WechatConfig",
  dicts: ["sys_wechat_config_type"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      wechatConfigList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        configName: undefined,
        configKey: undefined,
        configType: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "小程序名称不能为空", trigger: "blur" },
        ],
        type: [
          { required: true, message: "配置类型不能为空", trigger: "blur" },
        ],
        appid: [{ required: true, message: "APPID不能为空", trigger: "blur" }],
        secret: [
          { required: true, message: "SECRET不能为空", trigger: "blur" },
        ],
        msgDataFormat: [
          { required: true, message: "消息格式不能为空", trigger: "blur" },
        ],
      },
      tenantOptions: [],
      messageFormatOptions: [
        { label: "JSON", value: "JSON" },
        { label: "XML", value: "XML" },
      ],
    };
  },
  created() {
    this.getList();
    this.queryTenantOptions();
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true;
      getWechatConfigPage(this.queryParams).then((response) => {
        this.wechatConfigList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    queryTenantOptions() {
      queryOptions().then((response) => {
        this.tenantOptions = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        miniProgramId: undefined,
        tenantId: undefined,
        type: undefined,
        appid: undefined,
        secret: undefined,
        token: undefined,
        aesKey: undefined,
        issued: undefined,
        msgDataFormat: undefined,
        name: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加微信小程序";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.miniProgramId;
      getWechatConfig(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改微信小程序";
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.miniProgramId != undefined) {
            updateWechatConfig(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            saveWechatConfig(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /**
     * 微信配置状态修改
     * @param {配置信息} row
     */
    handleStatusChange(row) {
      let text = row.issued === true ? "启用" : "停用";
      this.$modal
        .confirm("确认要" + text + '"' + row.name + '"微信小程序吗？')
        .then(function () {
          return changeWechatConfigStatus(row.issued, row.miniProgramId);
        })
        .then(() => {
          this.$modal.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.issued = row.issued === true ? false : true;
        });
    },
    async goclick(row, column, event, cell) {
      try {
        if (column.property === "appid") {
          await navigator.clipboard.writeText(row["appid"]);
          this.$modal.msgSuccess("appid 已复制");
        }
        if (column.property === "token") {
          await navigator.clipboard.writeText(row["token"]);
          this.$modal.msgSuccess("token 已复制");
        }
        if (column.property === "aesKey") {
          await navigator.clipboard.writeText(row["aesKey"]);
          this.$modal.msgSuccess("aesKey 已复制");
        }
        if (column.property === "tenantName") {
          await navigator.clipboard.writeText(row["tenantId"]);
          this.$modal.msgSuccess("tenantId 已复制");
        }
      } catch (err) {
        console.error("Failed to copy: ", err);
      }
    },
    async toMerchantsPage(row) {
      const miniProgramId = row.miniProgramId;
      this.$router.push({
        name: "Merchants",
        params: { miniProgramId: miniProgramId },
      });
    },
    async toCommercialPage(row) {
      const miniProgramId = row.miniProgramId;
      this.$router.push({
        name: "Commercial",
        params: { miniProgramId: miniProgramId },
      });
    },
  },
};
</script>
