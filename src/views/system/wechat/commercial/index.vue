<template>
  <div class="app-container">
    <!-- <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="配置类型" prop="tenantId">
        <el-select
          v-model="queryParams.tenantId"
          placeholder="请选择配置类型"
          clearable
          filterable
          style="width: 240px"
        >
          <el-option
            v-for="tenant in tenantOptions"
            :key="tenant.tenantId"
            :label="tenant.companyName"
            :value="tenant.tenantId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入小程序名称" />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form> -->

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:wechat:mini:program:add']"
          >新增</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tenantMiniProgramList">
      <el-table-column
        label="消息类型"
        align="center"
        prop="reminderType"
        :formatter="formatArrayData"
      />
      <el-table-column
        label="模板消息ID"
        align="center"
        prop="templateId"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="APPID"
        align="center"
        prop="appid"
        :show-overflow-tooltip="true"
      />
      <!-- <el-table-column
        label="模板参数列表"
        align="center"
        prop="miniProgramName"
        :show-overflow-tooltip="true"
      >
      </el-table-column> -->
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-dropdown trigger="click">
            <el-button
              class="el-dropdown-link"
              size="mini"
              type="text"
              @click="handleUpadte(scope.row)"
            >
              修改
            </el-button>
            <el-button
              class="el-dropdown-link"
              size="mini"
              type="text"
              @click="handleLog(scope.row)"
            >
              查看
            </el-button>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :page-sizes="[3, 5]"
      @pagination="getList"
    />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <!-- <el-form-item label="商家名称" :label-width="formLabelWidth">
          <el-input v-model="form.name" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="小程序名称" :label-width="formLabelWidth">
          <el-input v-model="form.name" autocomplete="off"></el-input>
        </el-form-item> -->
        <el-form-item label="模板消息ID">
          <el-input v-model="form.templateId" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="消息类型" prop="reminderType">
          <el-select
            v-model="form.reminderType"
            placeholder="请选择消息类型"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="tenant in tenantOptions"
              :key="tenant.code"
              :label="tenant.description"
              :value="tenant.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="APPID">
          <el-input v-model="form.appid" autocomplete="off"></el-input>
        </el-form-item>

        <el-form-item label="模板参数列表">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="templateAdd"
            v-if="detailBtnIf"
          ></el-button>
          <div
            style="display: flex; margin-bottom: 10px; align-items: center"
            v-for="(item, index) in parameters"
            :key="index"
          >
            <div style="display: flex">
              <div>名称</div>
              <el-input
                v-model="item.name"
                autocomplete="off"
                style="width: 100px; margin-left: 10px"
                :disabled="!detailBtnIf"
              ></el-input>
            </div>
            <div style="display: flex; margin-left: 20px">
              <div>key</div>
              <el-input
                v-model="item.key"
                autocomplete="off"
                style="width: 100px; margin-left: 10px"
                :disabled="!detailBtnIf"
              ></el-input>
            </div>
            <!-- <i class="el-icon-delete" style="margin-left: 20rpx"></i> -->
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="detailBtnIf">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getTenantMiniProgramPage,
  getWechatConfig,
  generateQrCode,
  generateUrlLink,
  generateUrlScheme,
  saveTenantMiniProgram,
  saveUpdate,
  getAllReminderTypes,
  getTemplates,
  getDetail,
} from "@/api/system/wechat";
import { queryOptions } from "@/api/system/tenant";
import ImagePreview from "@/components/ImagePreview/index";

export default {
  name: "TenantMiniProgram",
  components: { ImagePreview },
  data() {
    return {
      detailBtnIf: true,
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      tenantMiniProgramList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 3,
        miniProgramId: undefined,
      },
      // 表单参数
      parameters: [
        {
          name: "",
          key: "",
        },
      ],
      form: {
        templateId: "",
        reminderType: "",
        miniProgramId: "",
      },
      // 表单校验
      rules: {
        tenantId: [
          { required: true, message: "商户不能为空", trigger: "blur" },
        ],
      },
      tenantOptions: [],
      messageFormatOptions: [
        { label: "JSON", value: "JSON" },
        { label: "XML", value: "XML" },
      ],
      miniProgramId: undefined,
      fullscreenLoading: false,
    };
  },
  created() {
    this.miniProgramId = this.$route.params.miniProgramId;
    if (this.$route.params.miniProgramId) {
      this.getList();
    } else {
      if (this.miniProgramId == undefined || this.miniProgramId == null) {
        this.$router.push({ path: "/wechat/MiniProgram" });
      }
    }
    this.queryTenantOptions();
    this.getAllReminderTypes();
  },
  methods: {
    formatArrayData(row, column, cellValue) {
      // 假设cellValue是数组，如[1, 2, 3]
      console.log(row.reminderType, column, cellValue);
      let description = "";
      this.tenantOptions.forEach((item) => {
        if (item.code == row.reminderType) {
          description = item.description;
        }
      });
      return description; // 转换为字符串显示
    },
    getDetail(id) {
      this.loading = true;
      let data = {
        id: id,
      };
      getDetail(data).then((response) => {
        this.form = response.data;
        this.parameters = response.data.parameters;
        this.loading = false;
      });
    },
    templateAdd() {
      this.parameters.push({
        name: "",
        key: "",
      });
    },
    /** 获取所有提醒类型 */
    getAllReminderTypes() {
      this.loading = true;
      getAllReminderTypes().then((response) => {
        this.tenantOptions = response.data;
        this.loading = false;
      });
    },
    /** 查询参数列表 */
    getList() {
      this.loading = true;
      if (this.miniProgramId == undefined || this.miniProgramId == null) {
        this.$router.push({ path: "/wechat/MiniProgram" });
      }
      this.queryParams.miniProgramId = this.miniProgramId;
      getTemplates(this.queryParams).then((response) => {
        this.tenantMiniProgramList = response.data;
        // this.total = response.total;
        this.loading = false;
      });
    },
    queryTenantOptions() {
      // queryOptions().then((response) => {
      //   this.tenantOptions = response.data;
      // });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        miniProgramId: undefined,
        tenantId: undefined,
        type: undefined,
        appid: undefined,
        secret: undefined,
        token: undefined,
        aesKey: undefined,
        issued: undefined,
        msgDataFormat: undefined,
        name: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      (this.form = {
        templateId: "",
        reminderType: "",
        miniProgramId: "",
      }),
        (this.parameters = [
          {
            name: "",
            key: "",
          },
        ]),
        (this.detailBtnIf = true);
      this.reset();
      this.open = true;
      this.title = "新增";
    },
    handleUpadte(row) {
      this.detailBtnIf = true;
      this.open = true;
      this.title = "修改";
      this.getDetail(row.id);
    },
    handleLog(row) {
      this.detailBtnIf = false;
      this.open = true;
      this.title = "详情";
      this.getDetail(row.id);
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.miniProgramId;
      getWechatConfig(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改微信小程序";
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.open = true;
          this.form.miniProgramId = this.miniProgramId;
          this.form.parameters = this.parameters;
          saveUpdate(this.form).then((response) => {
            this.$modal.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    async generateQrCode(row) {
      this.fullscreenLoading = true;
      let tenantConfigId = row.tenantConfigId;
      generateQrCode(tenantConfigId)
        .then((response) => {
          this.fullscreenLoading = false;
          this.$modal.msgSuccess("小程序码生成成功");
          this.getList();
        })
        .catch();
    },
    async generateUrlLike(row) {
      this.fullscreenLoading = true;
      let tenantConfigId = row.tenantConfigId;
      generateUrlLink(tenantConfigId)
        .then((response) => {
          this.fullscreenLoading = false;
          this.$modal.msgSuccess("小程序URL_LINK生成成功");
          this.getList();
        })
        .catch();
    },
    //生成URL_SCHEME
    async generateUrlScheme(row) {
      this.fullscreenLoading = true;
      let tenantConfigId = row.tenantConfigId;
      generateUrlScheme(tenantConfigId)
        .then((response) => {
          this.fullscreenLoading = false;
          this.$modal.msgSuccess("小程序URL_SCHEME生成成功");
          this.getList();
        })
        .catch();
    },
    async copyUrl(text) {
      try {
        await navigator.clipboard.writeText(text);
        this.$modal.msgSuccess("内容已复制");
      } catch (err) {
        this.$modal.msgError("内容复制失败");
      }
    },
  },
};
</script>
