<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="智能体名称" prop="agentName">
        <el-input
          v-model="queryParams.agentName"
          placeholder="请输入智能体名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="智能体编码" prop="agentCode">
        <el-input
          v-model="queryParams.agentCode"
          placeholder="请输入智能体编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="智能体状态" clearable>
          <el-option label="启用" value="0" />
          <el-option label="禁用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['ai:agent:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['ai:agent:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['ai:agent:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="agentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="智能体ID" align="center" prop="agentId" width="100" />
      <el-table-column label="头像" align="center" prop="avatarUrl" width="80">
        <template slot-scope="scope">
          <el-avatar v-if="scope.row.avatarUrl" :src="scope.row.avatarUrl" :size="40"></el-avatar>
          <el-avatar v-else icon="el-icon-user-solid" :size="40"></el-avatar>
        </template>
      </el-table-column>
      <el-table-column label="智能体名称" align="center" prop="agentName" :show-overflow-tooltip="true" />
      <el-table-column label="智能体编码" align="center" prop="agentCode" />
      <el-table-column label="描述" align="center" prop="agentDescription" :show-overflow-tooltip="true" />
      <el-table-column label="模型" align="center" prop="modelName" width="120" />
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="是否默认" align="center" prop="isDefault" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isDefault" type="success">是</el-tag>
          <el-tag v-else type="info">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['ai:agent:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-star-off"
            @click="handleSetDefault(scope.row)"
            v-hasPermi="['ai:agent:edit']"
            v-if="!scope.row.isDefault"
          >设为默认</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-copy-document"
            @click="handleCopy(scope.row)"
            v-hasPermi="['ai:agent:add']"
          >复制</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['ai:agent:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改智能体对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="智能体名称" prop="agentName">
              <el-input v-model="form.agentName" placeholder="请输入智能体名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="智能体编码" prop="agentCode">
              <el-input v-model="form.agentCode" placeholder="请输入智能体编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="模型名称" prop="modelName">
              <el-input v-model="form.modelName" placeholder="请输入模型名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="头像URL" prop="avatarUrl">
              <el-input v-model="form.avatarUrl" placeholder="请输入头像URL" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="描述" prop="agentDescription">
          <el-input v-model="form.agentDescription" type="textarea" placeholder="请输入描述" />
        </el-form-item>
        <el-form-item label="系统提示词" prop="systemPrompt">
          <el-input v-model="form.systemPrompt" type="textarea" :rows="4" placeholder="请输入系统提示词" />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="温度参数" prop="temperature">
              <el-input-number v-model="form.temperature" :min="0" :max="1" :step="0.1" :precision="1" placeholder="0.0-1.0" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最大Token" prop="maxTokens">
              <el-input-number v-model="form.maxTokens" :min="1" :max="8192" placeholder="最大输出token数" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="Top-p参数" prop="topP">
              <el-input-number v-model="form.topP" :min="0" :max="1" :step="0.1" :precision="1" placeholder="0.0-1.0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="频率惩罚" prop="frequencyPenalty">
              <el-input-number v-model="form.frequencyPenalty" :min="-2" :max="2" :step="0.1" :precision="1" placeholder="-2.0-2.0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="存在惩罚" prop="presencePenalty">
              <el-input-number v-model="form.presencePenalty" :min="-2" :max="2" :step="0.1" :precision="1" placeholder="-2.0-2.0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="启用记忆">
              <el-switch v-model="form.enableMemory"></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="启用RAG">
              <el-switch v-model="form.enableRag"></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="启用工具">
              <el-switch v-model="form.enableTools"></el-switch>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="记忆窗口大小" prop="memoryWindowSize" v-if="form.enableMemory">
          <el-input-number v-model="form.memoryWindowSize" :min="1" :max="100" placeholder="消息条数" />
        </el-form-item>
        <el-form-item label="知识库ID" prop="defaultKnowledgeIds" v-if="form.enableRag">
          <el-input v-model="form.defaultKnowledgeIds" placeholder="多个ID用逗号分隔" />
        </el-form-item>
        <el-form-item label="可用工具" prop="availableTools" v-if="form.enableTools">
          <el-input v-model="form.availableTools" type="textarea" placeholder="JSON格式的工具配置" />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio label="0">启用</el-radio>
                <el-radio label="1">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否公开">
              <el-switch v-model="form.isPublic"></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="排序" prop="sort">
              <el-input-number v-model="form.sort" :min="0" placeholder="排序值" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAgent, getAgent, delAgent, addAgent, updateAgent, changeAgentStatus, setDefaultAgent, copyAgent, checkAgentCodeUnique } from "@/api/ai/agent";

export default {
  name: "Agent",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 智能体表格数据
      agentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        agentName: null,
        agentCode: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        agentName: [
          { required: true, message: "智能体名称不能为空", trigger: "blur" }
        ],
        agentCode: [
          { required: true, message: "智能体编码不能为空", trigger: "blur" },
          { pattern: /^[a-zA-Z0-9_]+$/, message: "智能体编码只能包含字母、数字和下划线", trigger: "blur" }
        ],
        agentDescription: [
          { required: true, message: "描述不能为空", trigger: "blur" }
        ],
        systemPrompt: [
          { required: true, message: "系统提示词不能为空", trigger: "blur" }
        ],
        modelName: [
          { required: true, message: "模型名称不能为空", trigger: "blur" }
        ],
        temperature: [
          { type: 'number', min: 0, max: 1, message: "温度参数必须在0-1之间", trigger: "blur" }
        ],
        topP: [
          { type: 'number', min: 0, max: 1, message: "Top-p参数必须在0-1之间", trigger: "blur" }
        ],
        frequencyPenalty: [
          { type: 'number', min: -2, max: 2, message: "频率惩罚必须在-2到2之间", trigger: "blur" }
        ],
        presencePenalty: [
          { type: 'number', min: -2, max: 2, message: "存在惩罚必须在-2到2之间", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询智能体列表 */
    getList() {
      this.loading = true;
      listAgent(this.queryParams).then(response => {
        this.agentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        agentId: null,
        agentCode: null,
        agentName: null,
        agentDescription: null,
        avatarUrl: null,
        systemPrompt: null,
        modelName: null,
        temperature: 0.7,
        maxTokens: 2048,
        topP: 1.0,
        frequencyPenalty: 0.0,
        presencePenalty: 0.0,
        enableMemory: false,
        memoryWindowSize: 10,
        enableRag: false,
        defaultKnowledgeIds: null,
        enableTools: false,
        availableTools: null,
        status: "0",
        sort: 0,
        isPublic: false,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.agentId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加智能体";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const agentId = row.agentId || this.ids
      getAgent(agentId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改智能体";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.agentId != null) {
            updateAgent(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAgent(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 智能体状态修改 */
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.agentName + '"智能体吗？').then(function() {
        return changeAgentStatus(row.agentId, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.status = row.status === "0" ? "1" : "0";
      });
    },
    /** 设置默认智能体 */
    handleSetDefault(row) {
      this.$modal.confirm('确认要将"' + row.agentName + '"设置为默认智能体吗？').then(function() {
        return setDefaultAgent(row.agentId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("设置成功");
      }).catch(() => {});
    },
    /** 复制智能体 */
    handleCopy(row) {
      this.$prompt('请输入新的智能体编码', '复制智能体', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^[a-zA-Z0-9_]+$/,
        inputErrorMessage: '智能体编码格式不正确'
      }).then(({ value }) => {
        const newName = row.agentName + '_copy';
        copyAgent(row.agentId, value, newName).then(() => {
          this.getList();
          this.$modal.msgSuccess("复制成功");
        });
      }).catch(() => {});
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const agentIds = row.agentId || this.ids;
      this.$modal.confirm('是否确认删除智能体编号为"' + agentIds + '"的数据项？').then(function() {
        return delAgent(agentIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.mb8 {
  margin-bottom: 8px;
}

.el-input-number {
  width: 100%;
}

.dialog-footer {
  text-align: center;
}
</style>
