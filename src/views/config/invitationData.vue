<template>
  <div class="app-container home" id="app">
    <div class="dataStatistics">
      <div class="dataStatistics1_1">请帖数据总览</div>
      <div class="dataStatistics1">
        <div class="dataStatistics2">
          <ul class="dataStatistics2_1">
            <li
              :class="
                tabIndex == index ? 'StatisticsActive' : 'dataStatistics2_4'
              "
              v-for="(item, index) in listTab"
              :key="index"
              @click="tab(index)"
            >
              {{ item }}
            </li>
          </ul>
          <el-date-picker
            v-model="startDate"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择开始时间"
            class="dataStatistics2_2"
          >
          </el-date-picker>
          <el-date-picker
            v-model="endDate"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择结束时间"
            class="dataStatistics2_2"
          >
          </el-date-picker>
          <el-button type="primary" @click="inquire">查询</el-button>
          <!-- <p class="dataStatistics2_3">查看更多</p> -->
        </div>
        <div>
          <el-select v-model="endDate" placeholder="请选择活动区域">
            <el-option label="区域一" value="shanghai"></el-option>

            <el-option label="区域二" value="beijing"></el-option>
          </el-select>
        </div>
      </div>
    </div>
    <div class="dataStatistics8">
      <div class="informationNum">
        <div class="informationNum1">
          <div class="title">访客数</div>
          <div class="num">50</div>
        </div>
        <div class="informationNum1">
          <div class="title">访客数</div>
          <div class="num">50</div>
        </div>
        <div class="informationNum1">
          <div class="title">访客数</div>
          <div class="num">50</div>
        </div>
        <div class="informationNum1">
          <div class="title">访客数</div>
          <div class="num">50</div>
        </div>
        <div class="informationNum1">
          <div class="title">访客数</div>
          <div class="num">50</div>
        </div>
        <div class="informationNum1">
          <div class="title">访客数</div>
          <div class="num">50</div>
        </div>
        <div class="informationNum1">
          <div class="title">访客数</div>
          <div class="num">50</div>
        </div>
        <div class="informationNum1">
          <div class="title">访客数</div>
          <div class="num">50</div>
        </div>
        <div class="informationNum1">
          <div class="title">访客数</div>
          <div class="num">50</div>
        </div>
        <div class="informationNum1">
          <div class="title">访客数</div>
          <div class="num">50</div>
        </div>
        <div class="informationNum1">
          <div class="title">访客数</div>
          <div class="num">50</div>
        </div>
        <div class="informationNum1">
          <div class="title">访客数</div>
          <div class="num">50</div>
        </div>
      </div>
    </div>
    <div class="information">
      <p class="dataStatistics1_1">商家使用请帖排名</p>
      <div id="chartLineBox" style="width: 100%; height: 349px"></div>
    </div>
    <div class="information">
      <p class="dataStatistics1_1">请帖使用排名</p>
      <div class="tab">
        <div
          v-for="(item, index) in tabList"
          :key="index"
          :class="index == invitationIndex ? 'tabActive' : 'tab1'"
          @click="invitationTab(index)"
        >
          {{ item }}
        </div>
      </div>
      <div id="chartLineBox1" style="width: 100%; height: 349px"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { totalVisit, totalList, analysePage } from "@/api/platform/index";
export default {
  name: "Index",
  data() {
    return {
      invitationIndex: 0,
      tabList: ["全部", "长图", "翻页"],
      total: 0,
      analysePage: [],
      totalList: "",
      startDate: null, //开始时间
      endDate: null, //结束时间
      ruleForm: {},
      tabIndex: 0,
      listTab: ["日", "周", "月"],
      listData: [
        {
          productName: "1111111",
        },
      ],
      // 版本号
      version: "3.8.7",
      input: "",
      value1: "",
      from: {
        pageSize: 10,
        pageNum: 1,
      },
    };
  },
  created() {
    this.totalVisit();
    this.gettotalList();
    this.analysePages();
  },
  methods: {
    invitationTab(index) {
      this.invitationIndex = index;
    },
    handleSizeChange(val) {
      this.from.pageSize = val;
      this.analysePages();
    },
    handleCurrentChange(val) {
      this.from.pageNum = val;
      this.analysePages();
    },
    analysePages() {
      analysePage(this.from).then((res) => {
        if (res.code == 200) {
          this.analysePage = res.rows;
          this.total = res.total;
          this.$message(res.msg);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    gettotalList() {
      totalList().then((res) => {
        if (res.code == 200) {
          this.totalList = res.data;
          this.$message(res.msg);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },

    getEcharts1() {
      this.chartLine = echarts.init(document.getElementById("chartLineBox1"));
      // 指定图表的配置项和数据
      var option = {
        xAxis: {
          type: "category",
          data: [
            "Mon",
            "Tue",
            "Wed",
            "Thu",
            "Fri",
            "Sat",
            "Sun",
            "Fri",
            "Sat",
            "Sun",
          ],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: [120, 200, 150, 80, 70, 110, 130, 70, 110, 130],
            type: "bar",
            itemStyle: {
              color: function (params) {
                // 定义颜色数组，每个柱子使用不同颜色
                var colorList = [
                  "#FF715B",
                  "#34D1BF",
                  "#3F6FFF",
                  "#6665DD",
                  "#F96A75",
                  "#FDC955",
                  "#4B85FF",
                  "#7D82FF",
                  "#34D1BF",
                  "#21ABFF",
                ];
                return colorList[params.dataIndex];
              },
            },
          },
        ],
      };
      // 使用刚指定的配置项和数据显示图表。
      this.chartLine.setOption(option);
    },
    getEcharts() {
      this.chartLine = echarts.init(document.getElementById("chartLineBox"));
      // 指定图表的配置项和数据
      var option = {
        xAxis: {
          type: "category",
          data: [
            "Mon",
            "Tue",
            "Wed",
            "Thu",
            "Fri",
            "Sat",
            "Sun",
            "Fri",
            "Sat",
            "Sun",
          ],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: [120, 200, 150, 80, 70, 110, 130, 70, 110, 130],
            type: "bar",
            itemStyle: {
              color: function (params) {
                // 定义颜色数组，每个柱子使用不同颜色
                var colorList = [
                  "#FF715B",
                  "#34D1BF",
                  "#3F6FFF",
                  "#6665DD",
                  "#F96A75",
                  "#FDC955",
                  "#4B85FF",
                  "#7D82FF",
                  "#34D1BF",
                  "#21ABFF",
                ];
                return colorList[params.dataIndex];
              },
            },
          },
        ],
      };
      // 使用刚指定的配置项和数据显示图表。
      this.chartLine.setOption(option);
    },
    tab(index) {
      this.tabIndex = index;
      this.totalVisit();
      this.startDate = null; //开始时间
      this.endDate = null; //结束时间
    },
    inquire() {
      this.totalVisit();
    },
    totalVisit() {
      //查询
      if (this.startDate && this.endDate) {
        this.ruleForm.queryType = "custom";
      } else {
        this.ruleForm.queryType =
          this.tabIndex == 0
            ? "day"
            : this.tabIndex == 1
            ? "week"
            : this.tabIndex == 2
            ? "month"
            : "";
      }
      this.ruleForm.startDate = this.startDate;
      this.ruleForm.endDate = this.endDate;
      totalVisit(this.ruleForm).then((res) => {
        if (res.code == 200) {
          this.listData = res.data;
          this.$message(res.msg);
          let eventTime = [];
          let uv = [];
          res.data.userAccessAnalysisStats.forEach((item) => {
            eventTime.push(item.eventTime);
            uv.push(item.uv);
          });
          this.eventTime = eventTime;
          this.uv = uv;
          this.getEcharts();
          this.getEcharts1();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    goTarget(href) {
      window.open(href, "_blank");
    },
    details(row) {
      //用户详情
      this.$router.push({
        path: "/dataAnalysisDetails",
        query: {
          userId: row.userId,
        },
      });
    },
    totalAccess() {
      //总访用户量
      this.$router.push({
        path: "/totalAccess",
        query: {},
      });
    },
    TotalBrowsingTime() {
      //浏览总时间
      this.$router.push({
        path: "/totalBrowsingTime",
        query: {},
      });
    },
    totalVisits() {
      //总访问次数
      this.$router.push({
        path: "/totalVisits",
        query: {},
      });
    },
  },
  mounted() {},
};
</script>

<style scoped lang="scss">
#app {
  background: #f0f1f5;
  padding-bottom: 40px;
  padding: 20px 20px;
}
.dataDisplay {
  display: flex;
  justify-content: space-between;
  // line-height: 0;
  .dataDisplay1 {
    background-image: linear-gradient(#3f85ff, #0054e8);
    border-radius: 10px;
    width: 32%;
    padding: 24px 24px;
    position: relative;
    .dataDisplay1_1 {
      color: #ffffff;
      font-size: 18px;
    }
    .img1 {
      position: absolute;
      width: 201px;
      height: 121px;
      top: 0;
      right: 0;
      z-index: 99;
    }
    .img2 {
      position: absolute;
      width: 151px;
      height: 91px;
      bottom: 0;
      right: 0;
      z-index: 99;
    }
    .evaluationManagement3 {
      display: flex;
      align-items: center;
      .evaluationManagement3_1 {
        margin-left: 20px;
        z-index: 1000;
        .evaluationManagement3_2 {
          font-size: 14px;
          color: #ffffff;
        }
        .evaluationManagement3_3 {
          font-size: 30px;
          color: #ffffff;
          font-weight: bold;
          padding-top: 4px;
          z-index: 1000;
        }
      }
    }
  }
}
.dataStatistics1_1 {
  color: #17191a;
  font-size: 20px;
}
.dataStatistics8 {
  background: #fff;
  padding: 26px 50px;
  width: 100%;
  margin-top: 20px;
  border-radius: 10px;
}
.dataStatistics {
  background: #fff;
  padding: 20px 20px;
  width: 100%;
  margin-top: 20px;
  border-radius: 10px;
  .dataStatistics1 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .dataStatistics1_1 {
      color: #17191a;
      font-size: 20px;
    }
  }
  .dataStatistics2 {
    display: flex;
    align-items: center;
    .dataStatistics2_1 {
      display: flex;
      list-style-type: none;
      padding: 0;
      .dataStatistics2_4 {
        width: 38px;
        height: 25px;
        border: 1px solid #c8c9cd;
        color: #c8c9cd;
        text-align: center;
        line-height: 25px;
        border-radius: 2px;
        margin-right: 12px;
      }
      .StatisticsActive {
        width: 38px;
        height: 25px;
        background: #3f85ff;
        color: #ffffff;
        text-align: center;
        line-height: 25px;
        border-radius: 2px;
        margin-right: 12px;
      }
    }
    .dataStatistics2_2 {
      margin-right: 12px;
    }
    .dataStatistics2_3 {
      color: #3f85ff;
      font-size: 14px;
      margin-left: 10px;
    }
  }
  .dataStatistics3 {
    display: flex;
    margin-top: 20px;
    .dataStatistics3_1 {
      width: 19%;
      background: #fff;
      padding: 12px 16px;
      border: 1px solid #d9d9d9;
      border-radius: 5px;
      margin-left: 1%;
      .dataStatistics3_1_1 {
        display: flex;
        align-items: center;
        line-height: 0;
        .dataStatistics3_1_2 {
          color: #17191a;
          font-size: 14px;
          margin-right: 5px;
        }
      }
    }
    .dataStatistics3_2 {
      color: #17191a;
      font-size: 30px;
      font-weight: bold;
      margin-top: 16px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      width: 100%;
    }
    .dataStatistics3_3 {
      display: flex;
      line-height: 0;
      align-items: center;
      .dataStatistics3_3_1 {
        color: #7c7d81;
        font-size: 14px;
      }
      .dataStatistics3_3_2 {
        display: flex;
        margin-left: 22px;
        align-items: center;
        .dataStatistics3_3_3 {
          color: #f84343;
          font-size: 14px;
          margin-left: 8px;
        }
        #dataStatistics3_3_3 {
          color: #0bbd71;
          font-size: 14px;
          margin-left: 8px;
        }
      }
    }
  }
}
.information {
  margin-top: 20px;
  background: #fff;
  padding: 20px 20px;
  border-radius: 10px;
  width: 100%;
}
.table {
  margin-top: 20px;
  background: #fff;
  padding: 20px 20px;
  border-radius: 10px;
  width: 100%;
}
.block {
  text-align: right;
  margin-top: 20px;
}
.informationNum {
  display: grid;
  grid-template-columns: repeat(6, 1fr); /* 每行三个等宽列 */
  gap: 10px; /* 行与列之间的间隔 */
  .informationNum1 {
    .title {
      color: #7c7d81;
      font-size: 18px;
    }
    .num {
      color: #222633;
      font-size: 26px;
    }
  }
}
.tab {
  display: flex;
  font-size: 12px;
  .tabActive {
    padding: 6px 18px;
    color: #fff;
    border-radius: 3px;
    background: #3f85ff;
    margin-right: 10px;
    cursor: pointer;
  }
  .tab1 {
    padding: 6px 18px;
    color: #7c7d81;
    border-radius: 3px;
    border: 1px solid #dcdcdc;
    margin-right: 10px;
    cursor: pointer;
  }
}
</style>
