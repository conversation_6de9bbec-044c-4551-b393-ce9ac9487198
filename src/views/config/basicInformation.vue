<template>
  <div id="app">
    <!-- <div class="head">
            <div class="title">月子套房配置</div>
            <div class="title1">Pages/月子套房设置/新增房型</div>
        </div> -->
    <div class="content">
      <div v-for="(item, index) in bizGoodsBasicInfo" :key="index">
        <div v-if="index <= 1">
          <div class="brandTitle">{{ item.type }}</div>
          <div class="content1">
            <div class="title5">
              <p class="required">*</p>
              <p>标题</p>
            </div>
            <el-input
              type="text"
              placeholder="请输入标题"
              maxlength="10"
              class="input"
              v-model="item.title"
            ></el-input>
          </div>
          <div class="content1">
            <div class="title5">
              <p class="required">*</p>
              <p>描述</p>
            </div>
            <!-- <el-input
            type="textarea"
            v-model="item.description"
            style="width: 368px"
            placeholder="请输入描述信息"
          ></el-input> -->
            <editor
              :minHeight="300"
              style="width: 608px"
              v-model="item.description"
            ></editor>
          </div>
          <div class="content1">
            <div class="title5">
              <p>照片</p>
            </div>
            <div class="upload">
              <image-upload
                :oneAll="3"
                :limit="8"
                :isShowTip="false"
                v-model="item.photos"
              />
            </div>
          </div>
          <div class="fgx"></div>
        </div>
      </div>
      <div class="btn">
        <el-button type="primary" @click="confirm">确定</el-button>
        <!-- <el-button @click="cancel">取消</el-button> -->
      </div>
    </div>
  </div>
</template>

<script>
import FileUpload from "@/components/FileUpload/index";
import { addConfig } from "@/api/system/user";
import ImageUpload from "@/components/ImageUpload/index";
import ImgUpload from "@/components/ImgUpload/index";
import { page, save } from "@/api/platform/basicInformation";
import { giftList } from "@/api/platform/public";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
export default {
  name: "app",
  components: { ImageUpload, FileUpload, ElImageViewer, ImgUpload },
  dicts: [
    "platform_suite_outdoor_features",
    "platform_suite_facility_features",
    "platform_suite_media_features",
    "platform_suite_bathroom_facilities",
  ],
  data() {
    return {
      bizGoodsBasicInfo: [
        {
          title: "",
          type: "品牌介绍",
          description: "",
          photos: [],
        },
        {
          title: "",
          type: "会所设施",
          description: "",
          photos: [],
        },
        // {
        //   title: "",
        //   type: "护理团队",
        //   description: "",
        //   photos: [],
        // },
        // {
        //   title: "",
        //   type: "产后康复",
        //   description: "",
        //   photos: [],
        // },
        // {
        //   title: "",
        //   type: "月子膳食",
        //   description: "",
        //   photos: [],
        // },
      ],
    };
  },
  created() {
    this.page();
  },
  methods: {
    confirm() {
      //确定
      let bizGoodsBasicInfo = this.bizGoodsBasicInfo;
      for (let index = 0; index < bizGoodsBasicInfo.length; index++) {
        if (bizGoodsBasicInfo[index].title == "") {
          this.$message("请填写" + bizGoodsBasicInfo[index].type + "标题");
          return;
        }
        if (bizGoodsBasicInfo[index].description == "") {
          this.$message("请填写" + bizGoodsBasicInfo[index].type + "描述");
          return;
        }
        if (bizGoodsBasicInfo[index].photos.length == 0) {
          this.$message("请上传" + bizGoodsBasicInfo[index].type + "照片");
          return;
        }
      }
      save(this.bizGoodsBasicInfo).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.page();
          return;
        } else {
          this.$message(res.msg);
        }
      });
      // if (data.roomName == "") {
      //   this.$message("请输入房间名称");
      //   return;
      // }
      // if (!this.suiteId) {
      //   save(this.ruleForm).then((res) => {
      //     console.log(res);
      //     if (res.code == 200) {
      //       this.$message(res.msg);
      //       this.$router.go(-1);
      //       return;
      //     } else {
      //       this.$message(res.msg);
      //     }
      //   });
      // }
      // if (this.suiteId) {
      //   update(this.ruleForm).then((res) => {
      //     console.log(res);
      //     if (res.code == 200) {
      //       this.$message(res.msg);
      //       this.$router.go(-1);
      //       return;
      //     } else {
      //       this.$message(res.msg);
      //     }
      //   });
      // }
    },
    page() {
      //查询月子套房信息
      page().then((res) => {
        if (res.code == 200) {
          if (res.data.length > 0) {
            this.bizGoodsBasicInfo = res.data;
          }
          this.$message(res.msg);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;

  .title {
    font-size: 22px;
    font-weight: bold;
  }

  .title1 {
    font-size: 14px;
  }
}

.content {
  background: #ffff;
  margin: 10px 10px;
  border-radius: 5px;
  padding: 24px 50px;

  .title2 {
    font-size: 20px;
    color: #17191a;
    margin-bottom: 24px;
  }

  .table {
    margin-top: 20px;
  }

  .title5 {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
    width: 10%;

    .required {
      color: red;
    }
  }

  .content1 {
    display: flex;
    align-items: center;
    margin: 20px 0 10px 0;

    .input {
      width: 240px;
      height: 32px;
      border-radius: 3px;
    }

    .upload {
    }
  }
}

.title3 {
  display: flex;
  color: #45464a;
  font-size: 14px;
}

.sel {
  display: flex;
  align-items: center;
  margin-right: 10px;

  .selTitle {
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
  }

  #cars {
    border: 1px solid #dcdcdc;
    border-radius: 3px;
    color: #7c7d81;
    font-size: 14px;
    width: 129px;
    height: 32px;
    margin-left: 6px;
  }
}

.roomMessage {
  display: flex;
}

.postpartum {
  display: flex;
  align-items: center;
}

.inputs {
  width: 160px;
  height: 32px;
  border-radius: 3px;
}

.units {
  width: 30px;
  height: 32px;
  background: #dcdcdc;
  border: 1px solid #dcdcdc;
  text-align: center;
  line-height: 32px;
  font-size: 14px;
}

.jia {
  width: 16px;
  height: 3px;
  background: #17191a;
  margin: 0 8px;
}

.title7 {
  font-size: 14px;
  color: #606266;
  box-sizing: border-box;
  font-weight: bold;
  margin-right: 8px;
}

#postpartum {
  margin-left: 8px;
}

.checkboxList {
  width: 690px;

  .checkboxLists {
    width: 100px;
    margin-bottom: 8px;
  }
}

.btn {
  margin: 20px auto;
  text-align: center;
}

.auditContent {
  width: 100%;
  padding: 20px 24px;
  background: #ff6c11;
  border-radius: 6px;
  font-size: 14px;
  line-height: 14px;
}

.pass {
  display: flex;
  align-items: center;
  color: #000000;
}

.passCause {
  color: #000000;
  margin-left: 20px;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.label {
  width: 368px;
  height: 86px;
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  display: flex;
  flex-wrap: wrap;
  padding: 10px 10px;

  .labelName {
    color: #7c7d81;
    font-size: 14px;
    height: 24px;
    border-radius: 3px;
    line-height: 24px;
    background: #ebebeb;
    margin-right: 16px;
    padding: 0 8px;
  }

  .inputLabel {
    width: 156px;
    font-size: 12px;
  }
}

.elTag {
  margin-right: 5px;
  margin-bottom: 5px;
}

.hint {
  display: flex;
  color: #45464a;
  font-size: 14px;
  margin-top: 8px;

  .hints {
    display: flex;
    align-items: center;

    .hints1 {
      color: #ff6c11;
    }
  }
}

.viewLocations {
  display: flex;

  .viewLocation {
    padding: 5px 5px;
    border-radius: 10px;
    border: 1px solid #dcdcdc;
    margin-left: 10px;
    height: 150px;

    .viewLocation1 {
      color: #3f85ff;
      font-size: 14px;
      text-align: center;
      margin-top: 8px;
    }
  }
}
.brandTitle {
  color: #17191a;
  font-size: 16px;
  font-weight: bold;
}
.fgx {
  width: 100%;
  height: 1px;
  background: #ebebeb;
  margin: 10px 0;
}
</style>
