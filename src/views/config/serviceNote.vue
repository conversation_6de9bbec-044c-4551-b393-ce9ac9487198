<template>
  <div class="app">
    <!-- <div class="head">
          <div class="titles">话题管理</div>
          <div class="title1">Pages/宝妈社区/话题管理</div>
      </div> -->
    <div class="content">
      <div class="contentHead">
        <h2 class="title2"></h2>
        <div class="contentHeads">
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">笔记内容</p>
              <el-input
                v-model="ruleForm.content"
                placeholder="请输入笔记内容"
                style="width: 160px"
                clearable
              ></el-input>
            </div>
          </div>
          <!-- <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">发布人员</p>
              <el-input
                v-model="ruleForm.labelName"
                placeholder="请输入发布人员"
                clearable
              ></el-input>
            </div>
          </div> -->
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">发布人员</p>
              <el-select
                v-model="ruleForm.pcUserId"
                placeholder="请选择发布人员"
                clearable
                style="width: 150px; margin-left: 10px"
              >
                <el-option
                  v-for="(item, index) in fbnameList"
                  :key="index"
                  :label="item.name"
                  :value="item.userId"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">联系电话</p>
              <el-input
                v-model="ruleForm.tel"
                placeholder="请输入联系电话"
                style="width: 160px"
                clearable
              ></el-input>
            </div>
          </div>
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">发布时间</p>
              <div style="display: flex; margin-left: 10px">
                <el-date-picker
                  v-model="ruleForm.startDate"
                  type="date"
                  placeholder="发布开始时间"
                  value-format="yyyy-MM-dd"
                  style="width: 180px"
                >
                </el-date-picker>
                <el-date-picker
                  v-model="ruleForm.endDate"
                  type="date"
                  placeholder="发布结束时间"
                  value-format="yyyy-MM-dd"
                  style="width: 180px"
                >
                </el-date-picker>
              </div>
            </div>
          </div>

          <el-button
            type="primary"
            style="margin-left: 8px; height: 36px"
            @click="inquire"
            >查询</el-button
          >
          <el-button
            type="primary"
            style="margin-left: 8px; height: 36px"
            @click="reset"
            >重置</el-button
          >
        </div>
        <el-button
          type="primary"
          style="margin-left: 8px; height: 36px; margin-bottom: 10px"
          @click="addMaternitySuite"
          >新增</el-button
        >
        <el-table v-loading="loading" :data="listData" class="table">
          <el-table-column label="序号" align="center">
            <template scope="scope">
              <span v-text="scope.$index + 1"></span>
            </template>
          </el-table-column>
          <el-table-column
            label="笔记内容"
            prop="content"
            :show-overflow-tooltip="true"
            align="center"
          >
          </el-table-column>
          <el-table-column
            label="角色"
            prop="roleName"
            :show-overflow-tooltip="true"
            align="center"
          >
            <template scope="scope">
              <p v-if="scope.row.roleCode == 'NURSE'">护理</p>
              <p v-if="scope.row.roleCode == 'POSTPARTUM'">产康</p>
              <p v-if="scope.row.roleCode == 'CHEF'">厨师</p>
            </template>
          </el-table-column>
          <el-table-column label="姓名" prop="staffName" align="center" />
          <el-table-column label="宝妈姓名" prop="momName" align="center" />
          <el-table-column
            label="发布人员"
            prop="createByName"
            align="center"
          />
          <el-table-column label="联系电话" prop="createByTel" align="center" />
          <el-table-column label="发布时间" prop="createTime" align="center" />
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="compile(scope.row)"
                >修改</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div class="block">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="currentPage"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <el-dialog title="删除" :visible.sync="deleteDialogVisible" width="30%">
      <span>是否删除此服务笔记？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmDelete">确 定 删 除</el-button>
      </span>
    </el-dialog>

    <el-dialog title="新增" :visible.sync="dialogFormVisible">
      <el-form :model="form">
        <el-row>
          <el-col :span="12">
            <el-form-item label="角色" :label-width="formLabelWidth">
              <el-select
                v-model="form.roleCode"
                placeholder="请选择角色"
                @change="handleChange1"
              >
                <el-option
                  :label="item.dictLabel"
                  :value="item.dictValue"
                  v-for="(item, index) in topicStatus1"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="姓名" :label-width="formLabelWidth">
              <el-select v-model="form.userId" placeholder="请选择姓名">
                <el-option
                  :label="item.name"
                  :value="item.userId"
                  v-for="(item, index) in nameList"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="宝妈姓名" :label-width="formLabelWidth">
              <el-select v-model="form.momId" placeholder="请选择宝妈姓名">
                <el-option
                  :label="item.name"
                  :value="item.momId"
                  v-for="(item, index) in momList"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务天数" :label-width="formLabelWidth">
              <el-select v-model="form.timeNumber" placeholder="请选择服务天数">
                <el-option
                  :label="item.dictLabel"
                  :value="item.dictValue"
                  v-for="(item, index) in serveNumList"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="标签" :label-width="formLabelWidth">
          <el-select
            v-model="form.taskNodeId"
            placeholder="请选择标签"
            @change="handleChange2"
          >
            <el-option
              :label="item.nodeName"
              :value="item.taskNodeId"
              v-for="(item, index) in tabList"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="内容模版" :label-width="formLabelWidth">
          <el-switch v-model="delivery"></el-switch>
        </el-form-item>
        <el-form-item label="填写内容" :label-width="formLabelWidth">
          <el-input
            type="textarea"
            v-model="form.content"
            placeholder="请输入笔记内容"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="图片"
          prop="contentPhotos"
          :label-width="formLabelWidth"
        >
          <image-upload v-model="form.imgs" :limit="9" />
        </el-form-item>
        <el-form-item label="视频" prop="videos" :label-width="formLabelWidth">
          <file-upload :limit="1" v-model="form.videos" />
        </el-form-item>
      </el-form>
      <div v-if="delivery">
        <div class="fgx"></div>
        <div>
          <div class="tagList">
            <div
              :class="tagIndex == index ? 'activeTag' : 'tag'"
              v-for="(item, index) in listTemplateStyle"
              :key="index"
              @click="tagList(index, item.name)"
            >
              {{ item.name }}
            </div>
          </div>
          <div class="TemplateList">
            <div
              v-for="(item, index) in listTemplate"
              :key="index"
              :class="templateIndex == index ? 'activeTemplate' : 'template'"
              @click="templateClick(index.item.content)"
            >
              {{ item.content }}
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="getadd">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="新增风格" :visible.sync="dialogFormVisibles">
      <el-form :model="form">
        <el-form-item label="文案风格" :label-width="formLabelWidth">
          <el-input
            type="textarea"
            v-model="styleName"
            placeholder="请输入文案风格"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisibles = false">取 消</el-button>
        <el-button type="primary" @click="styleAdd">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  page,
  delTable,
  save,
  listByType,
  templateStyleAdd,
  templateStyle,
  roleCodeapi,
  getMomListapi,
  getDiaryTemplateapi,
  getDetailapi,
  getDeleteapi,
  update,
  roleCode,
} from "@/api/platform/serviceNote";
export default {
  name: "app",
  data() {
    return {
      currentPage: 1,
      fbnameList: [],
      delivery: true,
      momList: [],
      nameList: [],
      serveNumList: [
        {
          dictLabel: "入住第一天",
          dictValue: 1,
        },
        {
          dictLabel: "入住第二天",
          dictValue: 2,
        },
        {
          dictLabel: "入住第三天",
          dictValue: 3,
        },
        {
          dictLabel: "入住第四天",
          dictValue: 4,
        },
        {
          dictLabel: "入住第五天",
          dictValue: 5,
        },
        {
          dictLabel: "入住第六天",
          dictValue: 6,
        },
        {
          dictLabel: "入住第七天",
          dictValue: 7,
        },
        {
          dictLabel: "入住第八天",
          dictValue: 8,
        },
        {
          dictLabel: "入住第九天",
          dictValue: 9,
        },
        {
          dictLabel: "入住第十天",
          dictValue: 10,
        },
        {
          dictLabel: "入住第十一天",
          dictValue: 11,
        },
        {
          dictLabel: "入住第十二天",
          dictValue: 12,
        },
        {
          dictLabel: "入住第十三天",
          dictValue: 13,
        },
        {
          dictLabel: "入住第十四天",
          dictValue: 14,
        },
        {
          dictLabel: "入住第十五天",
          dictValue: 15,
        },
        {
          dictLabel: "入住第十六天",
          dictValue: 16,
        },
        {
          dictLabel: "入住第十七天",
          dictValue: 17,
        },
        {
          dictLabel: "入住第十八天",
          dictValue: 18,
        },
        {
          dictLabel: "入住第十九天",
          dictValue: 19,
        },
        {
          dictLabel: "入住第二十天",
          dictValue: 20,
        },
        {
          dictLabel: "入住第二十一天",
          dictValue: 21,
        },
        {
          dictLabel: "入住第二十二天",
          dictValue: 22,
        },
        {
          dictLabel: "入住第二十三天",
          dictValue: 23,
        },
        {
          dictLabel: "入住第二十四天",
          dictValue: 24,
        },
        {
          dictLabel: "入住第二十五天",
          dictValue: 25,
        },
        {
          dictLabel: "入住第二十六天",
          dictValue: 26,
        },
        {
          dictLabel: "入住第二十七天",
          dictValue: 27,
        },
        {
          dictLabel: "入住第二十八天",
          dictValue: 28,
        },
        {
          dictLabel: "入住第二十九天",
          dictValue: 29,
        },
        {
          dictLabel: "入住第三十天",
          dictValue: 30,
        },
        {
          dictLabel: "入住第三十一天",
          dictValue: 31,
        },
        {
          dictLabel: "入住第三十二天",
          dictValue: 32,
        },
        {
          dictLabel: "入住第三十三天",
          dictValue: 33,
        },
        {
          dictLabel: "入住第三十四天",
          dictValue: 34,
        },
        {
          dictLabel: "入住第三十五天",
          dictValue: 35,
        },
        {
          dictLabel: "入住第三十六天",
          dictValue: 36,
        },
        {
          dictLabel: "入住第三十七天",
          dictValue: 37,
        },
        {
          dictLabel: "入住第三十八天",
          dictValue: 38,
        },
        {
          dictLabel: "入住第三十九天",
          dictValue: 39,
        },
        {
          dictLabel: "入住第四十天",
          dictValue: 40,
        },
        {
          dictLabel: "入住第四十一天",
          dictValue: 41,
        },
        {
          dictLabel: "入住第四十二天",
          dictValue: 42,
        },
        {
          dictLabel: "入住第四十三天",
          dictValue: 43,
        },
        {
          dictLabel: "入住第四十四天",
          dictValue: 44,
        },
        {
          dictLabel: "入住第四十五天",
          dictValue: 45,
        },
        {
          dictLabel: "入住第四十六天",
          dictValue: 46,
        },
        {
          dictLabel: "入住第四十七天",
          dictValue: 47,
        },
        {
          dictLabel: "入住第四十八天",
          dictValue: 48,
        },
        {
          dictLabel: "入住第四十九天",
          dictValue: 49,
        },
        {
          dictLabel: "入住第五十天",
          dictValue: 50,
        },
        {
          dictLabel: "入住第五十一天",
          dictValue: 51,
        },
        {
          dictLabel: "入住第五十二天",
          dictValue: 52,
        },
        {
          dictLabel: "入住第五十三天",
          dictValue: 53,
        },
        {
          dictLabel: "入住第五十四天",
          dictValue: 54,
        },
        {
          dictLabel: "入住第五十五天",
          dictValue: 55,
        },
        {
          dictLabel: "入住第五十六天",
          dictValue: 56,
        },
        {
          dictLabel: "入住第五十七天",
          dictValue: 57,
        },
        {
          dictLabel: "入住第五十八天",
          dictValue: 58,
        },
        {
          dictLabel: "入住第五十九天",
          dictValue: 59,
        },
        {
          dictLabel: "入住第六十天",
          dictValue: 60,
        },
      ],
      templateIndex: 0,
      tagIndex: 0,
      listTag: ["全部", "专业", "责任心"],
      listTemplate: [],
      contentStyle: "",
      tabList: [],
      formLabelWidth: "80px",
      typeId: "",
      nurseId: "",
      form: {
        timeNumber: "",
        roleCode: "",
        content: "",
        imgs: "",
        videos: "",
        taskNodeId: "",
        userId: "",
        momId: "",
      },
      dialogFormVisible: false,
      loading: false,
      onlineStatus: [
        //在线状态
        {
          dictLabel: "已上线",
          dictValue: "1",
        },
        {
          dictLabel: "已下线",
          dictValue: "0",
        },
      ],
      templateList: [
        //模板类型
        {
          dictLabel: "服务笔记",
          dictValue: "服务笔记",
        },
        {
          dictLabel: "服务内容",
          dictValue: "服务内容",
        },
      ],
      topicStatus1: [
        //话题状态
        {
          dictLabel: "护理",
          dictValue: "NURSE",
        },
        {
          dictLabel: "产康",
          dictValue: "POSTPARTUM",
        },
        {
          dictLabel: "厨师",
          dictValue: "CHEF",
        },
      ],
      topicStatus: [
        //话题状态
        {
          dictLabel: "护理",
          dictValue: "NURSE",
        },
        {
          dictLabel: "产康",
          dictValue: "POSTPARTUM",
        },
        {
          dictLabel: "厨师",
          dictValue: "CHEF",
        },
        {
          dictLabel: "月嫂",
          dictValue: "MATERNITY_NANNY",
        },
        {
          dictLabel: "销售",
          dictValue: "SALES",
        },
      ],
      total: 0,
      ruleForm: {
        pageSize: 10,
        pageNum: 1,
        startDate: "",
        endDate: "",
        tel: "",
        pcUserId: "",
        content: "",
      },
      deleteDialogVisible: false,
      dialogFormVisibles: false,
      listData: [],
      templateId: "",
      styleName: "",
      listTemplateStyle: [],
      labelName: "",
      diaryId: "",
    };
  },
  watch: {
    // nurseId() {
    //   this.getlistByType();
    // },
    // typeId() {.contentStyle
    //   this.getlistByType();
    // },
  },
  created() {
    this.getList();
    this.getfbroleCode();
  },
  methods: {
    reset() {
      this.currentPage = 1;
      this.ruleForm = {
        pageSize: 10,
        pageNum: 1,
        startDate: "",
        endDate: "",
        tel: "",
        pcUserId: "",
        content: "",
      };
      this.getList();
    },
    getDetail(diaryId) {
      getDetailapi(diaryId).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          let data = res.data;
          this.templateStyle();
          this.getroleCode(data.roleCode);
          this.form.diaryId = data.diaryId;
          this.form.timeNumber = data.timeNumber;
          this.form.roleCode = data.roleCode;
          this.form.content = data.content;
          this.form.imgs = data.imgs;
          this.form.videos = data.videos;
          this.form.taskNodeId = data.taskNodeId;
          this.form.momId = data.momId;
          this.form.userId = data.userId;
          this.getlistByType(data.roleCode);
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    getDiaryTemplate() {
      //获取笔记模板
      this.loading = true;
      let data = {
        labelName: this.labelName,
      };
      if (this.contentStyle != "全部") {
        data.contentStyle = this.contentStyle;
      }
      getDiaryTemplateapi(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.listTemplate = res.data;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    handleChange2(value) {
      this.labelName = this.tabList.find(
        (obj) => obj.taskNodeId === value
      ).nodeName;
      this.form.taskNodeId = value;
      this.templateStyle();
      this.getDiaryTemplate();
    },
    templateClick(index, content) {
      this.form.content = content;
      this.templateIndex = index;
    },
    tagList(index, name) {
      this.tagIndex = index;
      this.contentStyle = name;
      this.getDiaryTemplate();
    },
    handleChange1(value) {
      this.getroleCode(value);
      this.getlistByType(value);
    },
    inquire() {
      //查询
      this.ruleForm.pageNum = 1;
      this.currentPage = 1;
      this.getList();
    },
    handleSizeChange(val) {
      this.ruleForm.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.ruleForm.pageNum = val;
      this.getList();
    },
    handleDelete(row) {
      this.diaryId = row.diaryId;
      this.deleteDialogVisible = true;
    },
    confirmDelete() {
      //确定删除
      getDeleteapi(this.diaryId).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.deleteDialogVisible = false;
          this.getList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    addMaternitySuite() {
      //新增
      this.form.timeNumber = "";
      this.form.roleCode = "";
      this.form.imgs = [];
      this.form.videos = [];
      this.form.taskNodeId = "";
      this.form.userId = "";
      this.form.momId = "";
      this.dialogFormVisible = !this.dialogFormVisible;
      this.getMomList();
    },
    addStyle() {
      this.dialogFormVisibles = !this.dialogFormVisibles;
    },
    getMomList() {
      this.loading = true;
      getMomListapi().then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.momList = res.data;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    getList() {
      //列表
      this.loading = true;
      page(this.ruleForm).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.listData = res.rows;
          this.total = res.total;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    styleAdd() {
      templateStyleAdd(this.form).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.dialogFormVisibles = !this.dialogFormVisibles;
          this.styleName = "";
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    templateStyle() {
      //列表
      this.loading = true;
      templateStyle().then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          let data = res.data;
          data.unshift({
            name: "全部",
          });
          this.listTemplateStyle = data;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    getadd() {
      //新增
      if (this.form.roleCode == "") {
        this.$message("请选择角色");
        return;
      }

      if (this.form.userId == "") {
        this.$message("请选择姓名");
        return;
      }
      if (this.form.momId == "") {
        this.$message("请选择宝妈姓名");
        return;
      }

      if (this.form.timeNumber == "") {
        this.$message("请选择服务天数");
        return;
      }
      if (this.form.taskNodeId == "") {
        this.$message("请选择标签");
        return;
      }
      if (this.form.content == "") {
        this.$message("填写内容");
        return;
      }
      this.loading = true;
      // let data = {
      //   nurseId: this.nurseId,
      //   typeId: this.typeId,
      //   labelName: this.form.labelName,
      //   content: this.form.content,
      //   contentStyle: this.contentStyle,
      //   type: this.form.type,
      // };
      // if (this.templateId) {
      //   data.templateId = this.templateId;
      // }
      if (this.form.diaryId) {
        update(this.form).then((res) => {
          if (res.code == 200) {
            this.$message(res.msg);
            this.getList();
            this.dialogFormVisible = !this.dialogFormVisible;
            this.form.timeNumber = "";
            this.form.roleCode = "";
            this.form.imgs = [];
            this.form.videos = [];
            this.form.taskNodeId = "";
            this.form.userId = "";
            this.form.momId = "";
            this.from.diaryId = "";
            this.loading = false;
            return;
          } else {
            this.$message(res.msg);
          }
        });
      } else {
        save(this.form).then((res) => {
          if (res.code == 200) {
            this.$message(res.msg);
            this.getList();
            this.dialogFormVisible = !this.dialogFormVisible;
            this.form.timeNumber = "";
            this.form.roleCode = "";
            this.form.imgs = [];
            this.form.videos = [];
            this.form.taskNodeId = "";
            this.form.userId = "";
            this.form.momId = "";
            this.loading = false;
            return;
          } else {
            this.$message(res.msg);
          }
        });
      }
    },
    compile(row) {
      this.getMomList();
      //编辑
      // this.typeId = row.typeId;
      // this.nurseId = row.nurseId;
      // this.form.labelName = row.labelName;
      // this.form.content = row.content;
      // this.templateId = row.templateId;
      // this.contentStyle = row.contentStyle;
      // this.dialogFormVisible = !this.dialogFormVisible;
      // this.form.type = row.type;
      // this.getlistByType();
      // this.templateStyle();
      this.getDetail(row.diaryId);
      this.dialogFormVisible = !this.dialogFormVisible;
    },
    getfbroleCode() {
      //通过角色编码获取人员
      this.loading = true;
      roleCode().then((res) => {
        if (res.code == 200) {
          this.fbnameList = res.data;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    getroleCode(roleCode) {
      //通过角色编码获取人员
      this.loading = true;
      roleCodeapi(roleCode).then((res) => {
        if (res.code == 200) {
          this.nameList = res.data;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    getlistByType(nurseRole) {
      this.loading = true;
      let data = {
        nurseRole: nurseRole,
      };
      listByType(data).then((res) => {
        if (res.code == 200) {
          this.tabList = res.data;
          console.log(res.data, this.form.taskNodeId);
          this.labelName = res.data.find(
            (obj) => obj.taskNodeId === this.form.taskNodeId
          ).nodeName;
          this.getDiaryTemplate();
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.TemplateList {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 每行三个等宽列 */
  gap: 10px; /* 行与列之间的间隔 */
}
.activeTemplate {
  padding: 20px 20px;
  background: #e8eeff;
  color: #3f6fff;
  font-size: 10px;
}
.template {
  padding: 20px 20px;
  background: #f9f9f9;
  color: #747272;
  font-size: 10px;
}
.tagList {
  display: flex;
  align-items: center;
  margin-top: 10px;
  width: 100%;
  overflow-x: auto;
  white-space: nowrap; /* 防止内部元素换行 */
}
.activeTag {
  padding: 4px 20px;
  background: #3f85ff;
  color: #ffffff;
  font-size: 12px;
  border-radius: 4px;
  margin-right: 20px;
  margin-bottom: 10px;
}
.tag {
  padding: 4px 20px;
  color: #747272;
  font-size: 12px;
  border-radius: 4px;
  border: 1px solid #dcdcdc;
  margin-right: 20px;
  margin-bottom: 10px;
}
.fgx {
  width: 100%;
  height: 10px;
  background: #f0f1f5;
}
.addList {
  display: flex;
}
.app {
  background: #f0f1f5;
  padding-bottom: 30px;
}
.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;
  .titles {
    font-size: 22px;
    font-weight: bold;
  }
  .title1 {
    font-size: 14px;
  }
}
.content {
  margin: 20px 20px;
  .contentHead {
    background: #ffff;
    padding: 5px 14px;
    border-radius: 10px;
    .title2 {
      color: #17191a;
    }
  }
}
.contentHeads {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.roomMessage {
  display: flex;
}
.sel {
  display: flex;
  align-items: center;
  margin-right: 10px;
  .selTitle {
    width: 30%;
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
  }
}
#cars {
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  color: #7c7d81;
  font-size: 14px;
  width: 179px;
  height: 32px;
  margin-left: 6px;
}

.block {
  text-align: right;
  margin-top: 20px;
}
</style>
