import request from '@/utils/request'

// 查询智能体列表
export function listAgent(query) {
  return request({
    url: '/ai/agent/list',
    method: 'get',
    params: query
  })
}

// 获取智能体详细信息
export function getAgent(agentId) {
  return request({
    url: '/ai/agent/' + agentId,
    method: 'get'
  })
}

// 根据编码获取智能体详细信息
export function getAgentByCode(agentCode) {
  return request({
    url: '/ai/agent/code/' + agentCode,
    method: 'get'
  })
}

// 新增智能体
export function addAgent(data) {
  return request({
    url: '/ai/agent',
    method: 'post',
    data: data
  })
}

// 修改智能体
export function updateAgent(data) {
  return request({
    url: '/ai/agent',
    method: 'put',
    data: data
  })
}

// 删除智能体
export function delAgent(agentIds) {
  return request({
    url: '/ai/agent/' + agentIds,
    method: 'delete'
  })
}

// 启用/禁用智能体
export function changeAgentStatus(agentId, status) {
  return request({
    url: '/ai/agent/changeStatus',
    method: 'put',
    params: {
      agentId: agentId,
      status: status
    }
  })
}

// 设置默认智能体
export function setDefaultAgent(agentId) {
  return request({
    url: '/ai/agent/setDefault/' + agentId,
    method: 'put'
  })
}

// 复制智能体
export function copyAgent(agentId, newCode, newName) {
  return request({
    url: '/ai/agent/copy',
    method: 'post',
    params: {
      agentId: agentId,
      newCode: newCode,
      newName: newName
    }
  })
}

// 校验智能体编码是否唯一
export function checkAgentCodeUnique(agentCode, agentId) {
  return request({
    url: '/ai/agent/checkAgentCodeUnique',
    method: 'get',
    params: {
      agentCode: agentCode,
      agentId: agentId
    }
  })
}

// 获取可用智能体列表（不分页）
export function getAvailableAgents() {
  return request({
    url: '/ai/agent/available',
    method: 'get'
  })
}

// 获取默认智能体
export function getDefaultAgent() {
  return request({
    url: '/ai/agent/default',
    method: 'get'
  })
}
