import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询微信配置列表
export function getWechatConfigPage(query) {
    return request({
        url: '/platform/wechat_mini_program/page',
        method: 'get',
        params: query
    })
}

// 修改微信配置
export function updateWechatConfig(data) {
    return request({
        url: '/platform/wechat_mini_program/update',
        method: 'put',
        data: data
    })
}

// 更改微信配置状态
export function changeWechatConfigStatus(status, wConfigId) {
    return request({
        url: `/platform/wechat_mini_program/change/${status}/${wConfigId}`,
        method: 'post'
    })
}

// 新增微信配置
export function saveWechatConfig(data) {
    return request({
        url: '/platform/wechat_mini_program/save',
        method: 'post',
        data: data
    })
}

// 查询微信配置详细
export function getWechatConfig(wConfigId) {
    return request({
        url: '/platform/wechat_mini_program/info/' + parseStrEmpty(wConfigId),
        method: 'get'
    })
}

// 查询微信小程序商户配置列表
export function getTenantMiniProgramPage(query) {
    return request({
        url: '/platform/tenant_mini_program/page',
        method: 'get',
        params: query
    })
}

// 生成小程序二维码
export function generateQrCode(tenantConfigId) {
    return request({
        url: '/platform/tenant_mini_program/qr_code/' + parseStrEmpty(tenantConfigId),
        method: 'get'
    })
}

// 生成小程序url_link
export function generateUrlLink(tenantConfigId) {
    return request({
        url: '/platform/tenant_mini_program/url_like/' + parseStrEmpty(tenantConfigId),
        method: 'get'
    })
}

// 生成小程序url_scheme
export function generateUrlScheme(tenantConfigId) {
    return request({
        url: '/platform/tenant_mini_program/url_scheme/' + parseStrEmpty(tenantConfigId),
        method: 'get'
    })
}

// 新增小程序商户配置
export function saveTenantMiniProgram(data) {
    return request({
        url: '/platform/tenant_mini_program/save',
        method: 'post',
        data: data
    })
}

// 查询微信用户列表
export function getWechatUserPage(query) {
    return request({
        url: '/platform/wechat_user/page',
        method: 'get',
        params: query
    })
}
// 更改微信用户状态
export function changeWechatUserStatus(status, userId) {
    return request({
        url: `/platform/wechat_user/change/${status}/${userId}`,
        method: 'post'
    })
}

// 新增微信用户
export function createWechatUser(data) {
    return request({
        url: `/platform/wechat_user/save`,
        method: 'post',
        data: data
    })
}

// 删除微信用户
export function removeWechatUser(userId) {
    return request({
        url: `/platform/wechat_user/remove/${userId}`,
        method: 'delete',
    })
}

// 修改微信用户
export function updateWechatUser(data) {
    return request({
        url: `/platform/wechat_user/update`,
        method: 'PUT',
        data: data
    })
}

// 查询微信用户详情
export function getWechatUser(id) {
    return request({
        url: `/platform/wechat_user/info/${id}`,
        method: 'get',
    })
}

// 通过员工id创建微信用户
export function creatUserForStaffId(staffId) {
    return request({
        url: `/platform/wechat_user/creatForStaffId/${staffId}`,
        method: 'get',
    })
}

// 通过客户id创建微信用户
export function creatForCustomerId(customerId) {
    return request({
        url: `/platform/wechat_user/creatForCustomerId/${customerId}`,
        method: 'get',
    })
}

// 查询微信角色列表
export function getRoles(params) {
    return request({
        url: `/platform/wechat_user/roles`,
        method: 'get',
        params: params
    })
}


// 查询小程序模板配置
export function getTemplates(query) {
  return request({
      url: '/platform/wechat_template_config/getTemplates',
      method: 'get',
      params: query
  })
}
// 新增修改消息模板配置
export function saveUpdate(data) {
  return request({
      url: '/platform/wechat_template_config/saveUpdate',
      method: 'post',
      data: data
  })
}
// 获取所有提醒类型
export function getAllReminderTypes(data) {
  return request({
      url: '/platform/wechat_template_config/getAllReminderTypes',
      method: 'get'
  })
}
// 获取推送模板详情
export function getDetail(query) {
  return request({
      url: '/platform/wechat_template_config/getDetail',
      method: 'get',
      params: query
  })
}
