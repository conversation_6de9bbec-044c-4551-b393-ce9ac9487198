import request from '@/utils/request'//动态模板
//分页查询笔记
export function page(query){
    return request({
      url: '/platform/diary/page',
      method: 'get',
      params: query
    })
  }
 // 通过角色编码获取人员
 export function roleCode() {
  return request({
    url: '/platform/diary/getMomTeam',
    method: 'get'
  })
}

  // 通过角色编码获取人员
  export function roleCodeapi(roleCode) {
    return request({
      url: '/platform/diary/getMomTeam?roleCode=' + roleCode,
      method: 'get'
    })
  }
  // 通过类型与角色查询标签列表
  export function listByType(data){
    return request({
      url: '/platform/task_node/listByType',
      method: 'post',
      data: data
    })
  }
  //获取可选择的优质宝妈列表(下拉)
  export function getMomListapi(query){
    return request({
      url: '/platform/diary/getMomList',
      method: 'get',
    })
  }


  //获取所有模板风格
export function templateStyle(query){
  return request({
    url: '/platform/template_style/list',
    method: 'get',
    params: query
  })
}

  //获取笔记模板
  export function getDiaryTemplateapi(query){
    return request({
      url: '/platform/diary/getDiaryTemplate',
      method: 'get',
      params: query
    })
  }

   //发布笔记
   export function save(data){
    return request({
      url: '/platform/diary/publish',
      method: 'post',
      data: data
    })
  }
   //修改笔记
   export function update(data){
    return request({
      url: '/platform/diary/update',
      method: 'post',
      data: data
    })
  }
    // 获取笔记详情
    export function getDetailapi(diaryId) {
      return request({
        url: '/platform/diary/getDetail?diaryId=' + diaryId,
        method: 'get'
      })
    }

       // 删除宝妈笔记
       export function getDeleteapi(diaryId) {
        return request({
          url: '/platform/diary/delete?diaryId=' + diaryId,
          method: 'get'
        })
      }
