import request from '@/utils/request'

// 分页查询角色列表
export function getRolePage(params) {
    return request({
        url: '/platform/wechat/role/page',
        method: 'get',
        params: params
    })
}

// 查询所有角色列表（用于下拉选择）
export function getRoleList(params) {
    return request({
        url: '/platform/wechat/role/list',
        method: 'get',
        params: params
    })
}

// 新增角色
export function createRole(data) {
    return request({
        url: '/platform/wechat/role/save',
        method: 'post',
        data: data
    })
}

// 修改角色
export function updateRole(data) {
    return request({
        url: '/platform/wechat/role/update',
        method: 'put',
        data: data
    })
}

// 删除角色
export function deleteRole(roleId) {
    return request({
        url: `/platform/wechat/role/remove/${roleId}`,
        method: 'delete'
    })
}

// 获取角色详情
export function getRoleDetail(roleId) {
    return request({
        url: `/platform/wechat/role/info/${roleId}`,
        method: 'get'
    })
}

// 查询平台员工角色列表（保留原有接口）
export function getEmployeeRoleList(params) {
    return request({
        url: '/platform/employee/role_list',
        method: 'get',
        params: params
    })
}

// 为角色分配权限
export function assignRolePermissions(data) {
    return request({
        url: '/platform/wechat/role/assign-perms',
        method: 'post',
        data: data
    })
}

// 获取角色的权限列表
export function getRolePermissions(roleId) {
    return request({
        url: `/platform/wechat/role/perms/${roleId}`,
        method: 'get'
    })
}

// 查询所有权限列表（用于下拉选择）
export function getPermissionList(params) {
    return request({
        url: '/platform/wechat/perms/list',
        method: 'get',
        params: params
    })
}
