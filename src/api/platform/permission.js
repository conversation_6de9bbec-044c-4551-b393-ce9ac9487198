import request from '@/utils/request'

// 分页查询权限列表
export function getPermissionPage(params) {
    return request({
        url: '/platform/wechat/perms/page',
        method: 'get',
        params: params
    })
}

// 查询所有权限列表（用于下拉选择）
export function getPermissionList(params) {
    return request({
        url: '/platform/wechat/perms/list',
        method: 'get',
        params: params
    })
}

// 新增权限
export function createPermission(data) {
    return request({
        url: '/platform/wechat/perms/save',
        method: 'post',
        data: data
    })
}

// 修改权限
export function updatePermission(data) {
    return request({
        url: '/platform/wechat/perms/update',
        method: 'put',
        data: data
    })
}

// 删除权限
export function deletePermission(permsId) {
    return request({
        url: `/platform/wechat/perms/remove/${permsId}`,
        method: 'delete'
    })
}

// 获取权限详情
export function getPermissionDetail(permsId) {
    return request({
        url: `/platform/wechat/perms/info/${permsId}`,
        method: 'get'
    })
}
